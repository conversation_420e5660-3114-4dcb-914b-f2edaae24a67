/**
 * 性能监控工具
 * 用于监控虚拟滚动组件的性能指标
 */

class PerformanceMonitor {
  constructor() {
    this.metrics = {
      scrollEvents: 0,
      renderTime: [],
      visibleItems: [],
      memoryUsage: [],
      fps: [],
      lastFrameTime: 0,
      frameCount: 0
    }
    
    this.isMonitoring = false
    this.rafId = null
    this.startTime = 0
  }

  start() {
    if (this.isMonitoring) return
    
    this.isMonitoring = true
    this.startTime = performance.now()
    this.metrics = {
      scrollEvents: 0,
      renderTime: [],
      visibleItems: [],
      memoryUsage: [],
      fps: [],
      lastFrameTime: this.startTime,
      frameCount: 0
    }
    
    this.monitorFPS()
    console.log('🚀 性能监控已启动')
  }

  stop() {
    if (!this.isMonitoring) return
    
    this.isMonitoring = false
    if (this.rafId) {
      cancelAnimationFrame(this.rafId)
    }
    
    this.generateReport()
  }

  // 记录滚动事件
  recordScrollEvent() {
    if (!this.isMonitoring) return
    this.metrics.scrollEvents++
  }

  // 记录渲染时间
  recordRenderTime(startTime, endTime) {
    if (!this.isMonitoring) return
    const renderTime = endTime - startTime
    this.metrics.renderTime.push(renderTime)
    
    // 只保留最近100次记录
    if (this.metrics.renderTime.length > 100) {
      this.metrics.renderTime.shift()
    }
  }

  // 记录可见项目数量
  recordVisibleItems(count) {
    if (!this.isMonitoring) return
    this.metrics.visibleItems.push(count)
    
    // 只保留最近100次记录
    if (this.metrics.visibleItems.length > 100) {
      this.metrics.visibleItems.shift()
    }
  }

  // 监控FPS
  monitorFPS() {
    if (!this.isMonitoring) return
    
    const now = performance.now()
    this.metrics.frameCount++
    
    if (now - this.metrics.lastFrameTime >= 1000) {
      const fps = this.metrics.frameCount
      this.metrics.fps.push(fps)
      this.metrics.frameCount = 0
      this.metrics.lastFrameTime = now
      
      // 只保留最近60秒的FPS记录
      if (this.metrics.fps.length > 60) {
        this.metrics.fps.shift()
      }
    }
    
    this.rafId = requestAnimationFrame(() => this.monitorFPS())
  }

  // 记录内存使用情况
  recordMemoryUsage() {
    if (!this.isMonitoring || !performance.memory) return
    
    const memory = {
      used: performance.memory.usedJSHeapSize,
      total: performance.memory.totalJSHeapSize,
      limit: performance.memory.jsHeapSizeLimit,
      timestamp: performance.now()
    }
    
    this.metrics.memoryUsage.push(memory)
    
    // 只保留最近50次记录
    if (this.metrics.memoryUsage.length > 50) {
      this.metrics.memoryUsage.shift()
    }
  }

  // 生成性能报告
  generateReport() {
    const duration = performance.now() - this.startTime
    const avgRenderTime = this.metrics.renderTime.length > 0 
      ? this.metrics.renderTime.reduce((a, b) => a + b, 0) / this.metrics.renderTime.length 
      : 0
    const maxRenderTime = this.metrics.renderTime.length > 0 
      ? Math.max(...this.metrics.renderTime) 
      : 0
    const avgVisibleItems = this.metrics.visibleItems.length > 0 
      ? this.metrics.visibleItems.reduce((a, b) => a + b, 0) / this.metrics.visibleItems.length 
      : 0
    const avgFPS = this.metrics.fps.length > 0 
      ? this.metrics.fps.reduce((a, b) => a + b, 0) / this.metrics.fps.length 
      : 0
    const minFPS = this.metrics.fps.length > 0 ? Math.min(...this.metrics.fps) : 0

    const report = {
      duration: Math.round(duration),
      scrollEvents: this.metrics.scrollEvents,
      scrollEventsPerSecond: Math.round(this.metrics.scrollEvents / (duration / 1000)),
      avgRenderTime: Math.round(avgRenderTime * 100) / 100,
      maxRenderTime: Math.round(maxRenderTime * 100) / 100,
      avgVisibleItems: Math.round(avgVisibleItems),
      avgFPS: Math.round(avgFPS),
      minFPS,
      memorySnapshots: this.metrics.memoryUsage.length
    }

    console.group('📊 虚拟滚动性能报告')
    console.log('⏱️  监控时长:', `${report.duration}ms`)
    console.log('📜 滚动事件总数:', report.scrollEvents)
    console.log('🔄 滚动频率:', `${report.scrollEventsPerSecond}/秒`)
    console.log('🎨 平均渲染时间:', `${report.avgRenderTime}ms`)
    console.log('⚠️  最大渲染时间:', `${report.maxRenderTime}ms`)
    console.log('👁️  平均可见项目:', report.avgVisibleItems)
    console.log('🎯 平均FPS:', report.avgFPS)
    console.log('📉 最低FPS:', report.minFPS)
    console.log('💾 内存快照数:', report.memorySnapshots)
    
    // 性能建议
    if (report.maxRenderTime > 16) {
      console.warn('⚠️  检测到渲染时间过长，可能影响流畅度')
    }
    if (report.minFPS < 30) {
      console.warn('⚠️  检测到FPS过低，建议优化')
    }
    if (report.scrollEventsPerSecond > 60) {
      console.warn('⚠️  滚动事件频率过高，建议增加节流')
    }
    
    console.groupEnd()
    
    return report
  }

  // 获取实时性能数据
  getRealTimeMetrics() {
    if (!this.isMonitoring) return null
    
    const currentFPS = this.metrics.fps.length > 0 ? this.metrics.fps[this.metrics.fps.length - 1] : 0
    const recentRenderTime = this.metrics.renderTime.slice(-10)
    const avgRecentRenderTime = recentRenderTime.length > 0 
      ? recentRenderTime.reduce((a, b) => a + b, 0) / recentRenderTime.length 
      : 0

    return {
      currentFPS,
      avgRecentRenderTime: Math.round(avgRecentRenderTime * 100) / 100,
      scrollEvents: this.metrics.scrollEvents,
      visibleItems: this.metrics.visibleItems.length > 0 ? this.metrics.visibleItems[this.metrics.visibleItems.length - 1] : 0
    }
  }
}

// 创建全局实例
const performanceMonitor = new PerformanceMonitor()

// 开发环境下自动启用
if (process.env.NODE_ENV === 'development') {
  // 添加全局快捷键
  if (typeof window !== 'undefined') {
    window.addEventListener('keydown', (e) => {
      // Ctrl+Shift+P 开始/停止监控
      if (e.ctrlKey && e.shiftKey && e.key === 'P') {
        if (performanceMonitor.isMonitoring) {
          performanceMonitor.stop()
        } else {
          performanceMonitor.start()
        }
      }
      // Ctrl+Shift+R 显示实时数据
      if (e.ctrlKey && e.shiftKey && e.key === 'R') {
        const metrics = performanceMonitor.getRealTimeMetrics()
        if (metrics) {
          console.log('📊 实时性能数据:', metrics)
        }
      }
    })
    
    console.log('🔧 性能监控快捷键:')
    console.log('  Ctrl+Shift+P: 开始/停止监控')
    console.log('  Ctrl+Shift+R: 显示实时数据')
  }
}

export default performanceMonitor
