<template>
  <div class="performance-test">
    <div class="controls">
      <h2>虚拟滚动性能测试</h2>
      <div class="control-buttons">
        <el-button @click="startMonitoring" :disabled="isMonitoring" type="primary">
          开始监控
        </el-button>
        <el-button @click="stopMonitoring" :disabled="!isMonitoring" type="danger">
          停止监控
        </el-button>
        <el-button @click="showRealTimeMetrics">显示实时数据</el-button>
        <el-button @click="generateTestData">生成测试数据</el-button>
      </div>
      
      <div class="metrics-display" v-if="realTimeMetrics">
        <div class="metric-item">
          <span class="metric-label">当前FPS:</span>
          <span class="metric-value" :class="getFPSClass(realTimeMetrics.currentFPS)">
            {{ realTimeMetrics.currentFPS }}
          </span>
        </div>
        <div class="metric-item">
          <span class="metric-label">平均渲染时间:</span>
          <span class="metric-value" :class="getRenderTimeClass(realTimeMetrics.avgRecentRenderTime)">
            {{ realTimeMetrics.avgRecentRenderTime }}ms
          </span>
        </div>
        <div class="metric-item">
          <span class="metric-label">滚动事件数:</span>
          <span class="metric-value">{{ realTimeMetrics.scrollEvents }}</span>
        </div>
        <div class="metric-item">
          <span class="metric-label">可见项目数:</span>
          <span class="metric-value">{{ realTimeMetrics.visibleItems }}</span>
        </div>
      </div>
    </div>

    <div class="test-container">
      <VirtualScrollGrid
        :categorized-data="testData"
        :item-height="200"
        :category-height="50"
        :columns-per-row="4"
        :gap="15"
        :vertical-gap="15"
        :buffer-size="1"
      >
        <template #default="{ template, index }">
          <div class="test-item">
            <div class="item-header">{{ template.name || `项目 ${index + 1}` }}</div>
            <div class="item-content">
              <div class="item-image"></div>
              <div class="item-text">
                这是一个测试项目，用于验证虚拟滚动的性能。
                项目路径: {{ template.path }}
              </div>
            </div>
          </div>
        </template>
      </VirtualScrollGrid>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import VirtualScrollGrid from './VirtualScrollGrid.vue'
import performanceMonitor from '../utils/performanceMonitor.js'

export default {
  name: 'PerformanceTest',
  components: {
    VirtualScrollGrid
  },
  setup() {
    const isMonitoring = ref(false)
    const realTimeMetrics = ref(null)
    const testData = ref({})
    const metricsInterval = ref(null)

    // 生成测试数据
    const generateTestData = () => {
      const categories = ['分类A', '分类B', '分类C', '分类D', '分类E']
      const data = {}
      
      categories.forEach(category => {
        data[category] = []
        const itemCount = Math.floor(Math.random() * 200) + 50 // 50-250个项目
        
        for (let i = 0; i < itemCount; i++) {
          data[category].push({
            name: `${category} - 项目 ${i + 1}`,
            path: `/test/${category.toLowerCase()}/item-${i + 1}.html`
          })
        }
      })
      
      testData.value = data
      console.log('🎯 生成测试数据完成:', Object.values(data).reduce((sum, items) => sum + items.length, 0), '个项目')
    }

    // 开始监控
    const startMonitoring = () => {
      performanceMonitor.start()
      isMonitoring.value = true
      
      // 每秒更新实时数据
      metricsInterval.value = setInterval(() => {
        realTimeMetrics.value = performanceMonitor.getRealTimeMetrics()
      }, 1000)
    }

    // 停止监控
    const stopMonitoring = () => {
      performanceMonitor.stop()
      isMonitoring.value = false
      realTimeMetrics.value = null
      
      if (metricsInterval.value) {
        clearInterval(metricsInterval.value)
        metricsInterval.value = null
      }
    }

    // 显示实时数据
    const showRealTimeMetrics = () => {
      const metrics = performanceMonitor.getRealTimeMetrics()
      if (metrics) {
        console.log('📊 实时性能数据:', metrics)
        realTimeMetrics.value = metrics
      } else {
        console.log('⚠️ 性能监控未启动')
      }
    }

    // FPS颜色分类
    const getFPSClass = (fps) => {
      if (fps >= 50) return 'good'
      if (fps >= 30) return 'warning'
      return 'bad'
    }

    // 渲染时间颜色分类
    const getRenderTimeClass = (time) => {
      if (time <= 8) return 'good'
      if (time <= 16) return 'warning'
      return 'bad'
    }

    onMounted(() => {
      generateTestData()
    })

    onUnmounted(() => {
      if (isMonitoring.value) {
        stopMonitoring()
      }
    })

    return {
      isMonitoring,
      realTimeMetrics,
      testData,
      startMonitoring,
      stopMonitoring,
      showRealTimeMetrics,
      generateTestData,
      getFPSClass,
      getRenderTimeClass
    }
  }
}
</script>

<style scoped>
.performance-test {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.controls {
  padding: 20px;
  background: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.controls h2 {
  margin: 0 0 15px 0;
  color: #333;
}

.control-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.metrics-display {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.metric-label {
  font-weight: 500;
  color: #666;
}

.metric-value {
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 3px;
}

.metric-value.good {
  background: #e8f5e8;
  color: #52c41a;
}

.metric-value.warning {
  background: #fff7e6;
  color: #fa8c16;
}

.metric-value.bad {
  background: #fff2f0;
  color: #ff4d4f;
}

.test-container {
  flex: 1;
  overflow: hidden;
}

.test-item {
  height: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
  transition: transform 0.2s ease;
}

.test-item:hover {
  transform: translateY(-2px);
}

.item-header {
  padding: 12px;
  background: #007bff;
  color: white;
  font-weight: 500;
  font-size: 14px;
}

.item-content {
  padding: 12px;
  height: calc(100% - 50px);
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.item-image {
  width: 100%;
  height: 60px;
  background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
  border-radius: 4px;
}

.item-text {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  flex: 1;
}
</style>
