<template>
  <div class="app-container">
    <!-- 左侧控制面板 -->
    <div class="control-panel">
      <div class="panel-header">
        <h1 class="app-title">
          <el-icon class="title-icon"><Document /></el-icon>
          HTML邮件模板预览
        </h1>
        <div class="app-description">
          高效预览和管理HTML邮件模板
        </div>
      </div>

      <!-- 搜索区域 -->
      <div class="search-section">
        <div class="search-label">
          <el-icon><Search /></el-icon>
          <span>搜索模板</span>
        </div>
        <el-input
          v-model="searchTerm"
          placeholder="输入模板名称或路径..."
          class="search-input"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section">
        <div class="stats-card">
          <div class="stat-item">
            <div class="stat-label">总模板数</div>
            <div class="stat-value">{{ totalTemplates }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">分类数</div>
            <div class="stat-value">{{ sortedCategories.length }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">当前显示</div>
            <div class="stat-value">{{ totalTemplates }}</div>
          </div>
        </div>
      </div>

      <!-- 分类列表 -->
      <div class="categories-section">
        <div class="section-title">
          <el-icon><Folder /></el-icon>
          <span>模板分类</span>
        </div>
        <div class="categories-list">
          <div
            v-for="category in sortedCategories"
            :key="category"
            class="category-item"
            @click="scrollToCategory(category)"
          >
            <div class="category-name">{{ category }}</div>
            <div class="category-count">{{ categorizedTemplates[category].length }}</div>
          </div>
        </div>
      </div>

      <!-- 使用提示 -->
      <div class="performance-tip">
        <el-icon><InfoFilled /></el-icon>
        <div class="tip-content">
          <div class="tip-title">快速导航</div>
          <div class="tip-text">点击左侧分类可快速跳转到对应模板区域</div>
        </div>
      </div>
    </div>

    <!-- 右侧虚拟滚动区域 -->
    <div class="content-area">
      <div class="content-header">
        <div class="content-title">
          <span>模板预览</span>
          <el-tag v-if="searchTerm" type="warning" closable @close="searchTerm = ''">
            搜索: {{ searchTerm }}
          </el-tag>
        </div>
      </div>

      <div class="virtual-scroll-container">
        <div v-if="totalTemplates > 0" class="template-list" ref="templateListRef">
          <div
            v-for="category in sortedCategories"
            :key="category"
            class="category-section"
            :ref="el => setCategoryRef(category, el)"
          >
            <div class="category-header">
              <div class="category-title">
                <el-icon><Folder /></el-icon>
                <span>{{ category }}</span>
                <el-tag type="info" size="small">{{ categorizedTemplates[category].length }}</el-tag>
              </div>
            </div>
            <div class="template-grid">
              <TemplateCard
                v-for="template in categorizedTemplates[category]"
                :key="template.path"
                :template="template"
              />
            </div>
          </div>
        </div>
        
        <!-- 搜索无结果时的提示 -->
        <div v-else-if="searchTerm" class="no-results-container">
          <el-icon><Search /></el-icon>
          <div class="no-results-content">
            <h3>没有找到匹配的模板</h3>
            <p>尝试使用不同的关键词搜索，或者清除搜索条件查看所有模板</p>
            <el-button @click="searchTerm = ''" type="primary">清除搜索</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TemplateCard from './TemplateCard.vue'
import templatesData from '../templates.json'
import { InfoFilled, Search, Loading, Document, Folder } from '@element-plus/icons-vue'

export default {
  name: 'EmailTemplateList',
  components: {
    TemplateCard,
    InfoFilled,
    Search,
    Loading,
    Document,
    Folder,
  },
  data() {
    return {
      searchTerm: '',
      templates: templatesData,
      categoryRefs: {} // 存储分类元素的引用
    }
  },
  computed: {
    templatesWithCategories() {
      return this.templates.map(template => ({
        ...template,
        category: this.extractCategoryFromPath(template.path)
      }))
    },
    filteredTemplates() {
      if (!this.searchTerm) {
        return this.templatesWithCategories
      }
      const searchLower = this.searchTerm.toLowerCase()
      return this.templatesWithCategories.filter(template => 
        template.name.toLowerCase().includes(searchLower) ||
        template.path.toLowerCase().includes(searchLower) ||
        template.category.toLowerCase().includes(searchLower)
      )
    },
    categorizedTemplates() {
      const categories = {}
      this.filteredTemplates.forEach(template => {
        if (!categories[template.category]) {
          categories[template.category] = []
        }
        categories[template.category].push(template)
      })
      return categories
    },
    sortedCategories() {
      return Object.keys(this.categorizedTemplates).sort()
    },
    totalTemplates() {
      return this.filteredTemplates.length
    }
  },
  methods: {
    extractCategoryFromPath(path) {
      // 从路径中提取目录名作为分类
      // 例如: '/cbi/account-suspended/pb/index.html' -> 'account-suspended'
      const parts = path.split('/')
      if (parts.length >= 2 && parts[1] === 'cbi') {
        return parts[2]
      }
      return '其他'
    },
    handleSearch() {
      // 搜索处理已经通过computed属性自动处理
      // 搜索后重置分类引用
      this.$nextTick(() => {
        this.categoryRefs = {}
      })
    },
    setCategoryRef(category, el) {
      // 设置分类元素的引用
      if (el) {
        this.categoryRefs[category] = el
      }
    },
    scrollToCategory(category) {
      // 滚动到指定分类
      const categoryElement = this.categoryRefs[category]
      const scrollContainer = this.$refs.templateListRef?.parentElement

      if (categoryElement && scrollContainer) {
        // 计算目标位置，减去一些偏移量以获得更好的视觉效果
        const containerRect = scrollContainer.getBoundingClientRect()
        const categoryRect = categoryElement.getBoundingClientRect()
        const scrollTop = scrollContainer.scrollTop
        const targetScrollTop = scrollTop + categoryRect.top - containerRect.top - 20

        // 平滑滚动到目标位置
        scrollContainer.scrollTo({
          top: targetScrollTop,
          behavior: 'smooth'
        })
      }
    }
  },
  watch: {
    // 监听搜索变化，重置分类引用
    filteredTemplates() {
      this.$nextTick(() => {
        this.categoryRefs = {}
      })
    }
  }
}
</script>

<style scoped>
.app-container {
  display: flex;
  height: 100vh;
  background: #f8fafc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 左侧控制面板 */
.control-panel {
  width: 350px;
  background: white;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止整个面板滚动 */
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.05);
  height: 100vh;
}

.panel-header {
  padding: 24px 20px 20px;
  border-bottom: 1px solid #f3f4f6;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.app-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
}

.title-icon {
  font-size: 24px;
}

.app-description {
  font-size: 14px;
  opacity: 0.9;
  margin: 0;
}

.search-section {
  padding: 20px;
  border-bottom: 1px solid #f3f4f6;
  flex-shrink: 0; /* 防止被压缩 */
}

.search-label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.search-input {
  width: 100%;
}

.stats-section {
  padding: 20px;
  border-bottom: 1px solid #f3f4f6;
  flex-shrink: 0; /* 防止被压缩 */
}

.stats-card {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
  padding: 16px 8px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}



.categories-section {
  padding: 20px 20px 10px 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许flex子项收缩 */
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.categories-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0; /* 允许flex子项收缩 */
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 8px;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
  cursor: pointer;
  user-select: none;
  min-width: 0; /* 防止内容溢出 */
}

.category-item:hover {
  background: #e0f2fe;
  border-color: #0ea5e9;
  transform: translateX(2px);
}

.category-item:active {
  transform: translateX(1px);
  background: #bae6fd;
}

.category-name {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  margin-right: 8px;
}

.category-count {
  font-size: 12px;
  color: #6b7280;
  background: #e5e7eb;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
  flex-shrink: 0; /* 防止数字被压缩 */
  min-width: 24px;
  text-align: center;
}

.performance-tip {
  padding: 16px 20px;
  background: #eff6ff;
  border-top: 1px solid #e5e7eb;
  display: flex;
  gap: 12px;
  align-items: flex-start;
  flex-shrink: 0; /* 防止被压缩 */
  margin-bottom: 10px;
}

.tip-content {
  flex: 1;
}

.tip-title {
  font-size: 14px;
  font-weight: 500;
  color: #1e40af;
  margin-bottom: 4px;
}

.tip-text {
  font-size: 12px;
  color: #3730a3;
  line-height: 1.5;
}

/* 右侧内容区域 */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  overflow: hidden;
}

.content-header {
  padding: 16px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.content-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.view-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.virtual-scroll-container {
  flex: 1;
  overflow-y: auto;
  background: #f8fafc;
}

.template-list {
  padding: 24px;
}

.category-section {
  margin-bottom: 40px;
}

.category-header {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e5e7eb;
}

.category-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
}

.no-results-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 60px;
  text-align: center;
  color: #6b7280;
}

.no-results-container .el-icon {
  font-size: 64px;
  margin-bottom: 24px;
  opacity: 0.5;
}

.no-results-content h3 {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: #374151;
}

.no-results-content p {
  font-size: 14px;
  line-height: 1.6;
  margin: 0 0 24px 0;
  max-width: 400px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .control-panel {
    width: 280px;
  }
  
  .stats-card {
    grid-template-columns: 1fr;
  }
  
  .template-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .app-container {
    flex-direction: column;
    height: auto;
  }
  
  .control-panel {
    width: 100%;
    height: auto;
    max-height: none;
  }
  
  .content-area {
    height: calc(100vh - 400px);
    min-height: 500px;
  }
  
  .content-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .template-grid {
    grid-template-columns: 1fr;
  }
}

/* 自定义滚动条 */
.control-panel::-webkit-scrollbar,
.virtual-scroll-container::-webkit-scrollbar,
.categories-list::-webkit-scrollbar {
  width: 6px;
}

.control-panel::-webkit-scrollbar-track,
.virtual-scroll-container::-webkit-scrollbar-track,
.categories-list::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.control-panel::-webkit-scrollbar-thumb,
.virtual-scroll-container::-webkit-scrollbar-thumb,
.categories-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.control-panel::-webkit-scrollbar-thumb:hover,
.virtual-scroll-container::-webkit-scrollbar-thumb:hover,
.categories-list::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style> 