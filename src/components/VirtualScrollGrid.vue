<template>
  <div
    class="virtual-scroll-container"
    :class="{ scrolling: isScrolling }"
    ref="containerRef"
    @scroll="handleScroll"
  >
    <div class="virtual-scroll-content" :style="{ height: totalHeight + 'px' }">
      <!-- 渲染可见的分类和项目 -->
      <div
        v-for="item in visibleItems"
        :key="item.key"
        :style="{
          position: 'absolute',
          transform: `translate3d(${item.left}px, ${item.top}px, 0)`,
          width: item.width + 'px',
          height: item.height + 'px',
          willChange: isScrolling ? 'transform' : 'auto'
        }"
        :class="item.type"
      >
        <!-- 分类标题 -->
        <div v-if="item.type === 'category'" class="category-title">
          {{ item.data.category }} ({{ item.data.count }})
        </div>
        
        <!-- 模板卡片 -->
        <div v-else-if="item.type === 'template'" class="template-wrapper">
          <slot :template="item.data" :index="item.index"></slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import performanceMonitor from '../utils/performanceMonitor.js'

export default {
  name: 'VirtualScrollGrid',
  props: {
    // 分类数据，格式：{ categoryName: [templates...] }
    categorizedData: {
      type: Object,
      required: true
    },
    // 每个模板卡片的高度
    itemHeight: {
      type: Number,
      default: 280 // 卡片高度 + padding
    },
    // 分类标题高度
    categoryHeight: {
      type: Number,
      default: 60
    },
    // 每行显示的列数（响应式）
    columnsPerRow: {
      type: Number,
      default: 3
    },
    // 网格间距
    gap: {
      type: Number,
      default: 20
    },
    // 垂直间距（行与行之间的间距）
    verticalGap: {
      type: Number,
      default: 20
    },
    // 额外的缓冲区域（屏幕高度的倍数）
    bufferSize: {
      type: Number,
      default: 1
    }
  },
  setup(props) {
    const containerRef = ref(null)
    const scrollTop = ref(0)
    const containerHeight = ref(0)
    const containerWidth = ref(0)
    
    // 优化的布局计算
    const layoutItems = computed(() => {
      const cacheKey = `${containerWidth.value}-${responsiveColumns.value}-${Object.keys(props.categorizedData).length}`

      // 检查布局缓存
      if (layoutCache.value.has(cacheKey)) {
        return layoutCache.value.get(cacheKey)
      }

      const items = []
      let currentTop = 0
      const columns = responsiveColumns.value

      // 如果容器宽度还没有初始化，返回空数组
      if (containerWidth.value === 0) {
        return items
      }

      const itemWidth = (containerWidth.value - (columns - 1) * props.gap) / columns

      // 使用 Object.entries 减少重复的键查找
      Object.entries(props.categorizedData).forEach(([category, templates]) => {
        // 跳过空分类
        if (!templates || templates.length === 0) {
          return
        }

        // 添加分类标题
        items.push({
          key: `category-${category}`,
          type: 'category',
          top: currentTop,
          left: 0,
          width: containerWidth.value,
          height: props.categoryHeight,
          data: { category, count: templates.length }
        })

        currentTop += props.categoryHeight

        // 批量计算模板项目位置
        const templateItems = []
        const totalRows = Math.ceil(templates.length / columns)

        for (let i = 0; i < templates.length; i++) {
          const template = templates[i]
          const row = Math.floor(i / columns)
          const col = i % columns

          templateItems.push({
            key: `template-${template.path}`,
            type: 'template',
            top: currentTop + row * (props.itemHeight + props.verticalGap),
            left: col * (itemWidth + props.gap),
            width: itemWidth,
            height: props.itemHeight,
            data: template,
            index: i
          })
        }

        items.push(...templateItems)

        // 计算分类占用的高度
        const categoryContentHeight = totalRows > 0 ?
          totalRows * props.itemHeight + (totalRows - 1) * props.verticalGap : 0
        currentTop += categoryContentHeight + props.verticalGap * 2
      })

      // 缓存布局结果
      layoutCache.value.set(cacheKey, items)

      // 清理旧的布局缓存
      if (layoutCache.value.size > 3) {
        const keys = Array.from(layoutCache.value.keys())
        const oldKeys = keys.slice(0, -3)
        oldKeys.forEach(key => layoutCache.value.delete(key))
      }

      return items
    })
    
    // 计算总高度
    const totalHeight = computed(() => {
      if (layoutItems.value.length === 0) return 0
      const lastItem = layoutItems.value[layoutItems.value.length - 1]
      return lastItem.top + lastItem.height
    })
    
    // 缓存相关变量
    const layoutCache = ref(new Map())
    const visibleItemsCache = ref(new Map())
    const lastCacheKey = ref('')

    // 优化的可见项目计算
    const visibleItems = computed(() => {
      const buffer = containerHeight.value * props.bufferSize
      const startY = scrollTop.value - buffer
      const endY = scrollTop.value + containerHeight.value + buffer

      // 创建缓存键
      const cacheKey = `${startY}-${endY}-${layoutItems.value.length}-${scrollVelocity.value > 2 ? 'fast' : 'normal'}`

      // 如果是高速滚动，使用更大的缓冲区和更少的项目
      if (scrollVelocity.value > 2) {
        const fastBuffer = buffer * 2
        const fastStartY = scrollTop.value - fastBuffer
        const fastEndY = scrollTop.value + containerHeight.value + fastBuffer

        // 高速滚动时，减少渲染的项目数量
        return layoutItems.value.filter(item => {
          return item.top + item.height >= fastStartY && item.top <= fastEndY
        }).slice(0, Math.min(50, layoutItems.value.length)) // 限制最大渲染数量
      }

      // 检查缓存
      if (visibleItemsCache.value.has(cacheKey) && cacheKey === lastCacheKey.value) {
        return visibleItemsCache.value.get(cacheKey)
      }

      // 计算可见项目
      const visible = layoutItems.value.filter(item => {
        return item.top + item.height >= startY && item.top <= endY
      })

      // 记录可见项目数量（性能监控）
      performanceMonitor.recordVisibleItems(visible.length)

      // 缓存结果
      visibleItemsCache.value.set(cacheKey, visible)
      lastCacheKey.value = cacheKey

      // 清理旧缓存（保留最近的5个）
      if (visibleItemsCache.value.size > 5) {
        const keys = Array.from(visibleItemsCache.value.keys())
        const oldKeys = keys.slice(0, -5)
        oldKeys.forEach(key => visibleItemsCache.value.delete(key))
      }

      return visible
    })
    
    // 滚动优化相关变量
    const isScrolling = ref(false)
    const scrollRAF = ref(null)
    const scrollTimeout = ref(null)
    const lastScrollTime = ref(0)
    const scrollVelocity = ref(0)
    const lastScrollTop = ref(0)

    // 优化的滚动处理函数
    const handleScroll = () => {
      if (!containerRef.value) return

      // 记录滚动事件（性能监控）
      performanceMonitor.recordScrollEvent()

      const currentTime = performance.now()
      const currentScrollTop = containerRef.value.scrollTop

      // 计算滚动速度
      if (currentTime - lastScrollTime.value > 0) {
        scrollVelocity.value = Math.abs(currentScrollTop - lastScrollTop.value) / (currentTime - lastScrollTime.value)
      }

      lastScrollTime.value = currentTime
      lastScrollTop.value = currentScrollTop

      // 如果滚动速度过快，使用更激进的优化策略
      const isHighVelocityScroll = scrollVelocity.value > 2

      // 取消之前的动画帧
      if (scrollRAF.value) {
        cancelAnimationFrame(scrollRAF.value)
      }

      // 使用 requestAnimationFrame 优化滚动性能
      scrollRAF.value = requestAnimationFrame(() => {
        const renderStartTime = performance.now()

        if (containerRef.value) {
          scrollTop.value = currentScrollTop
          isScrolling.value = true

          // 清除之前的超时
          if (scrollTimeout.value) {
            clearTimeout(scrollTimeout.value)
          }

          // 设置滚动结束检测
          scrollTimeout.value = setTimeout(() => {
            isScrolling.value = false
            scrollVelocity.value = 0

            // 记录内存使用情况
            performanceMonitor.recordMemoryUsage()
          }, isHighVelocityScroll ? 200 : 100)

          // 记录渲染时间
          const renderEndTime = performance.now()
          performanceMonitor.recordRenderTime(renderStartTime, renderEndTime)
        }
      })
    }
    
    // 更新容器尺寸
    const updateContainerSize = () => {
      if (containerRef.value) {
        containerHeight.value = containerRef.value.clientHeight
        containerWidth.value = containerRef.value.clientWidth
      }
    }
    
    // 响应式计算列数
    const responsiveColumns = computed(() => {
      if (containerWidth.value < 768) return 1 // 移动端
      if (containerWidth.value < 1200) return 2 // 平板
      return props.columnsPerRow // 桌面端使用传入的列数
    })

    // 响应式监听
    const resizeObserver = ref(null)

    onMounted(() => {
      updateContainerSize()

      // 监听容器尺寸变化
      if (window.ResizeObserver) {
        resizeObserver.value = new ResizeObserver(() => {
          updateContainerSize()
        })
        resizeObserver.value.observe(containerRef.value)
      }

      // 监听窗口尺寸变化
      window.addEventListener('resize', updateContainerSize)
    })
    
    onUnmounted(() => {
      // 清理滚动相关的资源
      if (scrollRAF.value) {
        cancelAnimationFrame(scrollRAF.value)
      }
      if (scrollTimeout.value) {
        clearTimeout(scrollTimeout.value)
      }

      // 清理观察器和事件监听器
      if (resizeObserver.value) {
        resizeObserver.value.disconnect()
      }
      window.removeEventListener('resize', updateContainerSize)

      // 清理缓存
      layoutCache.value.clear()
      visibleItemsCache.value.clear()
    })
    
    // 监听数据变化，重置滚动位置
    watch(() => props.categorizedData, () => {
      nextTick(() => {
        if (containerRef.value) {
          containerRef.value.scrollTop = 0
          scrollTop.value = 0
        }
      })
    })
    
    return {
      containerRef,
      visibleItems,
      totalHeight,
      handleScroll,
      responsiveColumns,
      isScrolling
    }
  }
}
</script>

<style scoped>
.virtual-scroll-container {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  /* 性能优化 */
  transform: translateZ(0);
  -webkit-overflow-scrolling: touch;
  /* 减少重绘 */
  contain: layout style paint;
  /* 优化滚动性能 */
  scroll-behavior: auto;
}

.virtual-scroll-content {
  position: relative;
  width: 100%;
  /* 性能优化 */
  transform: translateZ(0);
  contain: layout style;
}

.category-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  padding-bottom: 10px;
  border-bottom: 2px solid #007bff;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
}

.category {
  width: 100%;
}

.template-wrapper {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

.template-wrapper > * {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

/* 性能优化：为绝对定位的项目添加优化 */
.category,
.template-wrapper {
  /* 启用硬件加速 */
  transform: translateZ(0);
  /* 减少重绘和重排 */
  contain: layout style paint;
  /* 优化合成层 */
  backface-visibility: hidden;
  /* 减少内存使用 */
  pointer-events: auto;
}

/* 高速滚动时的优化 */
.virtual-scroll-container.scrolling .category,
.virtual-scroll-container.scrolling .template-wrapper {
  /* 高速滚动时减少渲染质量以提升性能 */
  image-rendering: optimizeSpeed;
  /* 禁用一些昂贵的CSS效果 */
  filter: none;
  box-shadow: none;
}
</style>
