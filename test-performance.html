<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>虚拟滚动性能测试</title>
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        
        .test-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            padding: 20px;
            background: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            z-index: 100;
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .metrics {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .metric {
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        
        .metric-label {
            font-size: 12px;
            color: #666;
            display: block;
        }
        
        .metric-value {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        
        .content {
            flex: 1;
            overflow: hidden;
            position: relative;
        }
        
        .scroll-container {
            height: 100%;
            overflow-y: auto;
            padding: 20px;
        }
        
        .item {
            height: 200px;
            margin-bottom: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: #333;
            transition: transform 0.2s ease;
        }
        
        .item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .fps-good { color: #28a745; }
        .fps-warning { color: #ffc107; }
        .fps-bad { color: #dc3545; }
        
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        
        .instructions h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        
        .instructions ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1>🚀 虚拟滚动性能测试</h1>
            
            <div class="instructions">
                <h3>测试说明</h3>
                <ul>
                    <li>点击"开始监控"开始性能监控</li>
                    <li>快速滚动页面测试性能</li>
                    <li>观察FPS和渲染时间指标</li>
                    <li>使用快捷键 Ctrl+Shift+P 开始/停止监控</li>
                    <li>使用快捷键 Ctrl+Shift+R 显示实时数据</li>
                </ul>
            </div>
            
            <div class="controls">
                <button id="startBtn" class="btn btn-primary">开始监控</button>
                <button id="stopBtn" class="btn btn-danger" disabled>停止监控</button>
                <button id="metricsBtn" class="btn btn-primary">显示实时数据</button>
                <button id="generateBtn" class="btn btn-primary">生成更多项目</button>
            </div>
            
            <div class="metrics" id="metrics" style="display: none;">
                <div class="metric">
                    <span class="metric-label">FPS</span>
                    <span class="metric-value" id="fps">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">渲染时间 (ms)</span>
                    <span class="metric-value" id="renderTime">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">滚动事件</span>
                    <span class="metric-value" id="scrollEvents">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">可见项目</span>
                    <span class="metric-value" id="visibleItems">--</span>
                </div>
            </div>
        </div>
        
        <div class="content">
            <div class="scroll-container" id="scrollContainer">
                <!-- 动态生成的项目将插入这里 -->
            </div>
        </div>
    </div>

    <script>
        // 简化的性能监控器
        class SimplePerformanceMonitor {
            constructor() {
                this.isMonitoring = false
                this.metrics = {
                    scrollEvents: 0,
                    renderTimes: [],
                    fps: 0,
                    frameCount: 0,
                    lastFrameTime: 0
                }
                this.rafId = null
            }

            start() {
                if (this.isMonitoring) return
                
                this.isMonitoring = true
                this.metrics = {
                    scrollEvents: 0,
                    renderTimes: [],
                    fps: 0,
                    frameCount: 0,
                    lastFrameTime: performance.now()
                }
                
                this.monitorFPS()
                console.log('🚀 性能监控已启动')
            }

            stop() {
                if (!this.isMonitoring) return
                
                this.isMonitoring = false
                if (this.rafId) {
                    cancelAnimationFrame(this.rafId)
                }
                
                console.log('⏹️ 性能监控已停止')
                this.generateReport()
            }

            recordScrollEvent() {
                if (!this.isMonitoring) return
                this.metrics.scrollEvents++
            }

            recordRenderTime(time) {
                if (!this.isMonitoring) return
                this.metrics.renderTimes.push(time)
                if (this.metrics.renderTimes.length > 60) {
                    this.metrics.renderTimes.shift()
                }
            }

            monitorFPS() {
                if (!this.isMonitoring) return
                
                const now = performance.now()
                this.metrics.frameCount++
                
                if (now - this.metrics.lastFrameTime >= 1000) {
                    this.metrics.fps = this.metrics.frameCount
                    this.metrics.frameCount = 0
                    this.metrics.lastFrameTime = now
                }
                
                this.rafId = requestAnimationFrame(() => this.monitorFPS())
            }

            getRealTimeMetrics() {
                if (!this.isMonitoring) return null
                
                const avgRenderTime = this.metrics.renderTimes.length > 0
                    ? this.metrics.renderTimes.reduce((a, b) => a + b, 0) / this.metrics.renderTimes.length
                    : 0

                return {
                    currentFPS: this.metrics.fps,
                    avgRecentRenderTime: Math.round(avgRenderTime * 100) / 100,
                    scrollEvents: this.metrics.scrollEvents,
                    visibleItems: document.querySelectorAll('.item').length
                }
            }

            generateReport() {
                const metrics = this.getRealTimeMetrics()
                if (metrics) {
                    console.group('📊 性能报告')
                    console.log('FPS:', metrics.currentFPS)
                    console.log('平均渲染时间:', metrics.avgRecentRenderTime + 'ms')
                    console.log('滚动事件数:', metrics.scrollEvents)
                    console.log('可见项目数:', metrics.visibleItems)
                    console.groupEnd()
                }
            }
        }

        // 初始化
        const monitor = new SimplePerformanceMonitor()
        let itemCount = 100

        // 生成测试项目
        function generateItems(count = itemCount) {
            const container = document.getElementById('scrollContainer')
            container.innerHTML = ''
            
            for (let i = 0; i < count; i++) {
                const item = document.createElement('div')
                item.className = 'item'
                item.textContent = `测试项目 ${i + 1}`
                container.appendChild(item)
            }
            
            console.log(`生成了 ${count} 个测试项目`)
        }

        // 滚动事件处理
        let scrollRAF = null
        document.getElementById('scrollContainer').addEventListener('scroll', () => {
            monitor.recordScrollEvent()
            
            if (scrollRAF) {
                cancelAnimationFrame(scrollRAF)
            }
            
            scrollRAF = requestAnimationFrame(() => {
                const startTime = performance.now()
                // 模拟渲染工作
                const endTime = performance.now()
                monitor.recordRenderTime(endTime - startTime)
            })
        })

        // 按钮事件
        document.getElementById('startBtn').addEventListener('click', () => {
            monitor.start()
            document.getElementById('startBtn').disabled = true
            document.getElementById('stopBtn').disabled = false
            document.getElementById('metrics').style.display = 'flex'
            
            // 开始更新实时数据
            updateMetrics()
        })

        document.getElementById('stopBtn').addEventListener('click', () => {
            monitor.stop()
            document.getElementById('startBtn').disabled = false
            document.getElementById('stopBtn').disabled = true
            document.getElementById('metrics').style.display = 'none'
        })

        document.getElementById('metricsBtn').addEventListener('click', () => {
            const metrics = monitor.getRealTimeMetrics()
            if (metrics) {
                console.log('📊 实时性能数据:', metrics)
            } else {
                console.log('⚠️ 性能监控未启动')
            }
        })

        document.getElementById('generateBtn').addEventListener('click', () => {
            itemCount += 100
            generateItems(itemCount)
        })

        // 更新实时指标显示
        function updateMetrics() {
            if (!monitor.isMonitoring) return
            
            const metrics = monitor.getRealTimeMetrics()
            if (metrics) {
                const fpsElement = document.getElementById('fps')
                fpsElement.textContent = metrics.currentFPS
                fpsElement.className = `metric-value ${getFPSClass(metrics.currentFPS)}`
                
                document.getElementById('renderTime').textContent = metrics.avgRecentRenderTime
                document.getElementById('scrollEvents').textContent = metrics.scrollEvents
                document.getElementById('visibleItems').textContent = metrics.visibleItems
            }
            
            setTimeout(updateMetrics, 1000)
        }

        function getFPSClass(fps) {
            if (fps >= 50) return 'fps-good'
            if (fps >= 30) return 'fps-warning'
            return 'fps-bad'
        }

        // 快捷键支持
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.shiftKey && e.key === 'P') {
                if (monitor.isMonitoring) {
                    document.getElementById('stopBtn').click()
                } else {
                    document.getElementById('startBtn').click()
                }
            }
            if (e.ctrlKey && e.shiftKey && e.key === 'R') {
                document.getElementById('metricsBtn').click()
            }
        })

        // 初始化页面
        generateItems()
        
        console.log('🔧 性能测试页面已加载')
        console.log('快捷键: Ctrl+Shift+P (开始/停止监控), Ctrl+Shift+R (显示数据)')
    </script>
</body>
</html>
